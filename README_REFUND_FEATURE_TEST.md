# 退款功能自动化测试文档

## 概述

`test-refund-feature.php` 是一个完整的自动化测试脚本，用于验证门店业绩分析中新增的退款金额和退款率功能。该测试覆盖了从退款审批到数据同步的完整业务流程。

## 测试范围

### 1. 核心功能测试
- ✅ 退款审批流程（RefundApprovalProcess.onAgree()）
- ✅ 订单退款金额更新逻辑
- ✅ 队列任务触发和处理（StorePerformanceAnalysisJob）
- ✅ 数据同步逻辑（StorePerformanceAnalysisService.dataInitial()）

### 2. 数据准确性验证
- ✅ OrderHeader.refund_amount 到 StorePerformanceAnalysis.refund_amount 的同步
- ✅ 退款率计算公式：退款金额 ÷ 实收业绩 × 100%
- ✅ BcHelper::percentage() 方法的计算精度
- ✅ 汇总数据的正确性

### 3. API接口测试
- ✅ StorePerformanceAnalysisService.search() 方法返回数据
- ✅ 新增字段 refund_amount 和 refund_rate 的存在性
- ✅ 数据格式和精度验证

### 4. 边界情况测试
- ✅ 退款金额为0的处理
- ✅ 实收金额为0时退款率的处理
- ✅ 大金额的精度测试

## 测试场景设计

### 测试数据设置
```
门店1: 
  - 订单1: 实收1000元, 退款100元
  - 订单2: 实收1500元, 退款200元  
  - 订单3: 实收800元, 退款0元
  - 预期汇总: 实收3300元, 退款300元, 退款率9.09%

门店2:
  - 订单1: 实收1200元, 退款150元
  - 订单2: 实收2000元, 退款300元
  - 预期汇总: 实收3200元, 退款450元, 退款率14.06%
```

## 使用方法

### 运行命令
```bash
cd /path/to/erp_saas
php test-refund-feature.php
```

### 运行前提条件
1. 数据库连接正常
2. 相关模型类已加载
3. 测试用户具有必要权限
4. 无冲突的测试数据

### 预期输出
```
=================================================
          退款功能完整测试开始
=================================================

【步骤1】初始化测试环境...
  - 清理可能存在的测试数据...
  - 测试环境初始化完成

【步骤2】创建测试数据...
  - 创建多门店多订单测试场景...
  - 创建了 5 个测试订单
  - 涉及 2 个门店

【步骤3】退款审批流程测试...
  - 测试订单 TEST_REFUND_xxx 的退款审批流程...
    ✓ 订单 TEST_REFUND_xxx 退款金额正确设置为 100

【步骤4】队列任务处理测试...
  - 手动触发队列任务处理...
  ✓ 队列任务执行完成

【步骤5】数据同步验证...
  - 验证门店业绩分析数据同步...
  ✓ 门店1: 退款金额 300, 实收金额 3300
  ✓ 门店2: 退款金额 450, 实收金额 3200

【步骤6】退款率计算验证...
  - 验证退款率计算逻辑...
  ✓ 门店1: 退款率 9.09% (退款金额: 300, 实收金额: 3300)
  ✓ 门店2: 退款率 14.06% (退款金额: 450, 实收金额: 3200)

【步骤7】API接口验证...
  - 验证API接口返回数据...
  ✓ 门店1: API返回退款金额 300, 退款率 9.09%
  ✓ 门店2: API返回退款金额 450, 退款率 14.06%

【步骤8】边界情况测试...
  - 测试边界情况...
  ✓ 边界情况测试通过

【步骤9】清理测试数据...
  - 清理测试数据...
  - 测试数据清理完成

=================================================
                测试报告
=================================================
总测试数: 15
通过数: 15
失败数: 0

成功率: 100.00%

🎉 所有测试通过！退款功能工作正常。
=================================================
```

## 测试失败排查

### 常见问题

#### 1. 数据库连接问题
```
错误: 测试订单创建失败
解决: 检查数据库配置和连接状态
```

#### 2. 队列任务执行失败
```
错误: 队列任务执行结果 - 期望为true，实际为false
解决: 检查StorePerformanceAnalysisService.dataInitial()方法的逻辑
```

#### 3. 数据同步不一致
```
错误: 门店X退款金额同步验证 - 期望值: 300, 实际值: 0
解决: 检查SQL查询中是否正确包含refund_amount字段
```

#### 4. 退款率计算错误
```
错误: 门店X退款率计算验证 - 期望值: 9.09%, 实际值: 0.00%
解决: 检查BcHelper::percentage()方法的调用
```

### 调试方法

1. **开启详细输出**：修改测试文件中的echo语句以获取更多调试信息
2. **检查数据库状态**：测试失败时查看相关表的数据状态
3. **单步调试**：注释部分测试步骤，逐个验证功能
4. **日志查看**：检查应用日志中的错误信息

## 代码覆盖范围

### 核心文件
- `common/components/feishu/process/RefundApprovalProcess.php`
- `common/models/backend/order/OrderHeader.php`
- `common/queues/StorePerformanceAnalysisJob.php`
- `auth/services/data/StorePerformanceAnalysisService.php`
- `common/helpers/BcHelper.php`

### 前端文件（通过API验证）
- `manageSystem/src/views/theDataAnalysis/analysis/components/StorePerformanceAnalysisTab.vue`
- `manageSystem/src/views/theDataAnalysis/analysis/components/StoreColumns.js`

## 维护说明

### 测试数据清理
测试会自动清理所有创建的测试数据，包括：
- 测试订单（order_no以TEST_REFUND_开头）
- 测试退款申请（application_no以TEST_REFUND_开头）
- 测试日期的门店业绩分析数据

### 测试扩展
如需添加新的测试场景：
1. 在`createTestData()`函数中添加新的测试数据
2. 在相应的测试函数中添加验证逻辑
3. 更新`cleanupTestData()`函数以清理新数据

### 性能考虑
- 测试创建的数据量较小，适合开发环境
- 生产环境运行前请备份数据库
- 大量数据测试需要调整测试场景规模

## 总结

该测试脚本提供了退款功能的全链路验证，确保从业务流程到数据展示的每个环节都能正确工作。通过自动化测试可以快速发现问题并验证修复效果，提高开发效率和代码质量。