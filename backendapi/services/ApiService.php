<?php

/**
 * Api服务
 */

namespace backendapi\services;

use common\cache\WxcomCache;
use common\components\promoteData\Oceanengine;
use common\components\promoteData\Quickly;
use common\components\promoteData\Tencent;
use common\components\wechat\AccessToken;
use common\components\yxt\AppletSDK;
use common\enums\AdsAccountSubStatusEnum;
use common\enums\AdsAccountWayEnum;
use common\enums\AppletStatusEnum;
use common\enums\PlatformEnum;
use common\enums\PromotionEventTypeEnum;
use common\enums\ReportEventEnum;
use common\helpers\Tool;
use common\models\backendapi\PromoteChannel;
use common\models\common\AdsAccount;
use common\models\common\AdsAccountSub;
use common\models\common\ApiLog;
use common\models\common\Applet;
use common\models\common\Entity;
use common\models\common\LandPage;
use common\models\common\PromotionUserAnalysisLog;
use common\models\wxcom\CusDynamicQrcode;
use common\models\wxcom\UserQrcode;
use common\queues\CheckAppletStatusJob;
use Exception;
use services\common\DingExamineService;
use Yii;

class ApiService
{
    public $error = '';

    /**
     * 上报给第三方
     * @param array $params 数据源
     * @param int $id 上报对应的落地页的id
     * @param int $isInsert 是否是insert状态
     * @throws \yii\db\Exception
     */
    public static function reportThirdPlatform($params, $id, $isInsert)
    {
        if ($params['event'] == 'visit' && $params['platform_name'] == PlatformEnum::QUICKLY && $isInsert === 1) {
            $callbackData = [
                'unionid' => $params['unionid'],
                'analysis_id' => $id,
                'user_id' => '',
                'event_type' => PromotionEventTypeEnum::VISIT, //事件类型为访问
                'company_name' => $params['company_name'] ?: '',
                'created_at' => time()
            ];
            Yii::$app->db->createCommand()->insert(PromotionUserAnalysisLog::tableName(), $callbackData)->execute();
        }
    }

    /**
     * 获取腾讯广告access_token
     *
     * @param $authorization_code
     * @return mixed
     * @throws Exception
     */
    public static function getAdAccessToken($authorization_code)
    {
        $url = 'https://api.e.qq.com/oauth/token';
        $config = Yii::$app->params['wxAd'];
        $parameters = [
            'client_id' => $config['client_id'],
            'client_secret' => $config['client_secret'],
            'grant_type' => 'authorization_code',
            'authorization_code' => $authorization_code
        ];
        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }
        $request_url = $url . '?' . http_build_query($parameters);

        return Tool::curlRequest($request_url);
    }

    /**
     * 生成跳转微信小程序链接
     * @param array $params
     * @return bool|mixed|string
     * @throws \yii\db\Exception
     */
    public function createWeChatUrl($params = [])
    {
        $data = self::generateLink($params);
        if (!$data) {
            throw new Exception('小程序链接生成失败');
        }
        return $data['openlink'];
    }

    /**
     * 生成跳转小程序链接
     *
     * @param array $params
     * @return mixed|string[]
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public static function generateLink($params = [])
    {
        if (!empty($params['page_id'])) {
            $page = LandPage::findOne($params['page_id']);

            // 营销通小程序落地页
            if (!empty($page) && !empty($page->yxt_page_id)) {
                if (empty($params['pageQueryBody'])) {
                    throw new Exception('落地页配置异常');
                }

                // 解码前端数据
                $newParams = [];
                foreach ($params['pageQueryBody'] as $key => $item) {
                    if (!is_string($item)) {
                        continue;
                    }
                    $newParams[$key] = urldecode($item);
                }

                // 修复客服复制错地址，从cl中解析出真实的cl
                if (strpos($newParams['cl'], 'cl=')) {
                    $newParams['cl'] = explode('cl=', $newParams['cl'])[1];
                }

                // 生成reqId
                $reqInfo = [
                    'clickid' => $newParams['clickid'],
                    'callback' => $newParams['callback'],
                ];
                $reqId = md5(json_encode($reqInfo));
                $newParams['req_id'] = $reqId;

                // 落地页包含动态码时获取对应的二维码
                $content = json_decode($page->detail->content, true);
                if (empty($content['dynamicQrCode'])) {
                    throw new Exception('落地页动态码配置异常');
                }
                $qrcodeId = $content['dynamicQrCode']['id'];
                $qrcode = CusDynamicQrcode::findOne($qrcodeId);
                $user = $qrcode->getNextWxcomUser();
                $newParams['qrcode_id'] = $qrcodeId;
                $newParams['user_id'] = $user->id;
                $eventData = [
                    'unionid' => $newParams['unionid'],
                    'landing_id' => $page->id,
                    'cl' => $newParams['cl'] ?: 0,  //渠道参数
                    'event' => $newParams['event'] ?: '',  //事件类型
                    'UA' => $newParams['ua'] ?: '',
                    'date' => date('Ymd', time()),
                    'time' => date('Y-m-d H:i:s', time()),
                    'share' => $newParams['share'] ?: 0,
                    'params' => json_encode($newParams, 256),
                    'third_callback' => '',
                    'qrcode_id' => $newParams['qrcode_id'],
                    'user_id' => $newParams['user_id'],
                    'req_id' => $newParams['req_id'],
                ];
                $userQrcode = $user->useQrcode($eventData);
                $appletSDK = new AppletSDK($page->com->yxt_corp_id);
                $ret = $appletSDK->getLink($page->yxt_page_id, $userQrcode->media_id);
                return ['openlink' => $ret['Scheme']['URLScheme']];
            }
        }

        // 指定小程序id时获取对应小程序
        if ($params['app_id']) {
            $appletConfig = Applet::find()->where(['appid' => $params['app_id']])->one();
            // 小程序被禁用后去相同主体小程序
            if ($appletConfig && $appletConfig->status != AppletStatusEnum::ENABLED) {
                $appletConfig = Applet::find()->where(['owner' => $appletConfig->owner, 'status' => AppletStatusEnum::ENABLED, 'entity_id' => $appletConfig->entity_id])->one();
            }
        }
        // 前面逻辑未取到小程序，随机取一个可用小程序
        if (!$appletConfig) {
            $page = LandPage::findOne($params['page_id']);
            $appletConfig = Applet::find()->where(['status' => AppletStatusEnum::ENABLED, 'entity_id' => $page->entity_id])->one();
        }

        if (!$appletConfig) {
            throw new Exception('找不到可用小程序');
        }

        Yii::$app->redis->setex('AppletCreateLink:' . $appletConfig['appid'] . ':' . time(), 2 * 60, 1);
        CheckAppletStatusJob::addJob([
            'appId' => $appletConfig['appid'],
            'appName' => $appletConfig['name'],
        ]);

        try {
            $access_token = (new AccessToken($appletConfig['appid'], $appletConfig['app_secret']))->getAccessToken();
        } catch (Exception $e) {
            $errCode = $e->getCode();
            if ($errCode == '50002') {
                Yii::$app->notice->important('小程序接口被限制', '', [
                    '小程序名称' => $appletConfig['name'],
                    'appid' => $appletConfig['appid'],
                ]);
            }
            throw new Exception($e->getMessage(), $errCode);
        }

        // 链接过期时间为24小时
        $time = time() + 86400;
        $params = [
            'jump_wxa' => [
                'path' => '/pages/index/index',
                'query' => "{$params['url']}",
                // 'env_version' => 'release',
            ],
            'is_expire' => true,
            'expire_type' => 0,
            'expire_time' => $time,
        ];
        $url = 'https://api.weixin.qq.com/wxa/generatescheme?access_token=' . $access_token;
        $data = Tool::curlRequest($url, $params, true);
        if (isset($data['errcode']) && $data['errcode']) {
            $data['post_params'] = $params;
            $logData = [
                'type' => '422',
                'code' => $data['errcode'],
                'content' => '生成跳转小程序链接失败',
                'desc' => '',
                'callback_pack' => json_encode($data),
                'created_at' => time()
            ];
            Yii::$app->db->createCommand()->insert(ApiLog::tableName(), $logData)->execute();
            throw new Exception($data['errmsg'], $data['errcode']);
        }
        return $data;
    }

    /**
     * 获取微信openid/unionId
     *
     * @param $app_id
     * @param $code
     * @param $cl
     * @return array|mixed|string
     * @throws \yii\db\Exception
     */
    public function getWeChatOpenId($app_id, $code, $cl)
    {
        $applet_config = Applet::find()->where(['appid' => $app_id])->limit(1)->asArray()->one();
        $wxUrl = 'https://api.weixin.qq.com/sns/jscode2session?appid=' . $applet_config['appid'] . '&secret=' . $applet_config['app_secret'] . '&js_code=' . $code . '&grant_type=authorization_code';
        $data = Tool::curlRequest($wxUrl);
        if (isset($data['errcode']) && $data['errcode']) {
            return false;
            $logData = [
                'type' => '423',
                'code' => $data['errcode'],
                'content' => '微信小程序code换取unionId失败',
                'desc' => '渠道标识:' . $cl . ' appid:' . $app_id . ' code:' . $code,
                'callback_pack' => json_encode($data),
                'created_at' => time()
            ];
            Yii::$app->db->createCommand()->insert(ApiLog::tableName(), $logData)->execute();
            $DingData = ['content' => '微信小程序code换取unionId失败：' . json_encode($logData)];
            DingExamineService::sendDingDing($DingData, DingExamineService::getReportDing());
            return false;
        }
        return $data;
    }

    /**
     * 新落地页数据录入
     *
     * @param $params
     * @return array|bool
     * @throws \yii\db\Exception
     */
    public function newUserPromotion($params)
    {
        $ip = Tool::getClientIp();
        $event_name = $params['event'];
        $valid = [
            'unionid' => $params['unionid'],
            'req_id' => $params['req_id'],
            'landing_id' => $params['landing_id'] ?: 0,
            'cl' => $params['cl'] ?: 0,  //渠道参数
            'event' => $params['event'] ?: '',  //事件类型
            'UA' => $ip . $_SERVER['HTTP_USER_AGENT'],  //事件类型
            'date' => date('Ymd', time()),
            'time' => date('Y-m-d H:i:s', time()),
            'share' => $params['share'] ?: 0,
            'params' => json_encode($params, 256),
            'third_callback' => '',
        ];

        if ($params['share'] == 1) {
            $event_name = 'share_' . $params['event'];
        }

        $fieldName = $params['unionid'];
        if (!$params['unionid']) {
            if (!$params['req_id']) {
                $fieldName = substr(md5($ip . $_SERVER['HTTP_USER_AGENT']), 8, 16);
            } else {
                $fieldName = $params['req_id'];
            }
        }

        try {
            $wxcomCache = new WxcomCache();
            $keyName = $wxcomCache::Pre . $params['cl'] . '_' . $valid['date'] . ':' . $event_name;
            if ($event_name == 'visit') { //如果是visit情况下，判断是否有第三方回传参数
                if (isset($params['callback']) || isset($params['clickid'])) {
                    $wxcomCache->newHset($keyName, $fieldName, $valid);
                }
            } elseif ($event_name == 'views' || $event_name == 'share_views') {
                $wxcomCache->newHset($keyName, $fieldName . substr(time(), -6), $valid);
            } else {
                $wxcomCache->newHset($keyName, $fieldName, $valid);
            }

            if (($event_name == 'qrcode' || $event_name == 'share_qrcode') && $params['unionid']) {
                $wxcomCache->setex('unionid:' . $params['unionid'], 1800, json_encode($valid, JSON_UNESCAPED_UNICODE));

                $apiLog = new ApiLog();
                $apiLog->type = $event_name . '_unionid';
                $apiLog->code = '200';
                $apiLog->content = json_encode($valid, JSON_UNESCAPED_UNICODE);
                $apiLog->desc = $params['unionid'] ?: '';
                $apiLog->save();
            }

            if ($event_name == ReportEventEnum::CLICK_CONSULT) {
                //企微客服链路,用于后续匹配对应落地页的发送消息使用
                $wxcomCache->setex(ReportEventEnum::CLICK_CONSULT . ':' . $params['scene_param'], (86400 * 5), json_encode($valid, JSON_UNESCAPED_UNICODE));
                $wxcomCache->setex(ReportEventEnum::CLICK_CONSULT . ':' . $params['scene_param'] . '-key', (86400 * 5), json_encode(['keyName' => $keyName, 'field' => $fieldName], JSON_UNESCAPED_UNICODE));
            }

            return true;
        } catch (Exception $e) {
            $logData = $e->getMessage();
            Yii::$app->db->createCommand()->insert(ApiLog::tableName(), $logData)->execute();
            return false;
        }
    }

    /**
     * 新落地页数据录入
     *
     * @param $params
     * @return array|bool
     * @throws \yii\db\Exception
     */
    public function newUserPromotion2($params)
    {
        if (isset($params['app_id']) && $params['app_id']) {
            Yii::$app->redis->setex('AppletViews:' . $params['app_id'] . ':' . time(), 2 * 60, 1);
        }

        $eventName = $params['event'];
        $eventData = [
            'unionid' => $params['unionid'],
            'req_id' => $params['req_id'],
            'landing_id' => $params['landing_id'] ?: 0,
            'cl' => $params['cl'] ?: 0,  //渠道参数
            'event' => $params['event'] ?: '',  //事件类型
            'UA' => $params['ua'] ?: '',
            'date' => date('Ymd', time()),
            'time' => date('Y-m-d H:i:s', time()),
            'share' => $params['share'] ?: 0,
            'params' => json_encode($params, 256),
            'third_callback' => '',
        ];

        if ($params['share'] == 1) {
            $eventName = 'share_' . $params['event'];
        }

        $userKey = $params['unionid'];
        if (!$params['unionid']) {
            if (!$params['req_id']) {
                $userKey = substr(md5($eventData['UA']), 8, 16);
            } else {
                $userKey = $params['req_id'];
            }
        }

        $redisKeyPre = WxcomCache::Pre;
        try {
            $wxcomCache = new WxcomCache();
            switch ($eventName) {
                case 'qrcode':
                case 'share_qrcode':
                    $wxcomCache->setex('unionid:' . $params['unionid'], 1800, json_encode($eventData, 256));

                    $apiLog = new ApiLog();
                    $apiLog->type = $eventName . '_unionid';
                    $apiLog->code = '200';
                    $apiLog->content = json_encode($eventData, 256);
                    $apiLog->desc = $params['unionid'] ?: '';
                    $apiLog->save();
                    break;
                case 'views':
                case 'share_views':
                    $wxcomCache->newHset($redisKeyPre . $params['cl'] . '_' . $eventData['date'] . ':visit', $userKey, $eventData);
                    $userKey .= substr(time(), -6);
                    break;
            }

            $wxcomCache->newHset($redisKeyPre . $params['cl'] . '_' . $eventData['date'] . ':' . $eventName, $userKey, $eventData);
            return true;
        } catch (Exception $e) {
            $logData = $e->getMessage();
            Yii::$app->db->createCommand()->insert(ApiLog::tableName(), $logData)->execute();
            return false;
        }
    }

    /**
     * 巨量引擎-获取授权账号信息
     *
     * @param array $params
     * @return bool|mixed|string
     */
    public static function getOceanengineCallback($params = [])
    {
        $auth_code = $params["auth_code"];
        $state = $params["state"];
        if (empty($auth_code)) return '巨量引擎授权auth_code不能为空';
        //        if (empty($state)) return '您授权的链接state不能为空';

        //        $entity_id = Entity::find()->where(['code' => $state])->scalar();
        $entity_id = 1;
        if (empty($entity_id)) return '您授权的企业不存在您的系统，请查看链接';

        $access_token = (new Oceanengine())->getOcAccessToken($auth_code);
        if ($access_token['code'] != 0) return $access_token;
        if (count($access_token['data']['advertiser_ids']) < 1) return '巨量引擎没有授权账号';

        try {
            $trans = Yii::$app->db->beginTransaction();
            foreach ($access_token['data']['advertiser_ids'] as $advertiser_id) {
                $url = OceanEngine::baseUrl . 'open_api/2/user/info/';
                $userInfo = Tool::promoteGet($url, '', $access_token['data']['access_token']);
                $advertiser_user_id = empty($userInfo['data']['id']) ? 0 : $userInfo['data']['id'];

                /** @var $advertiserModel AdsAccount * */
                $advertiserModel = AdsAccount::findOne([
                    'advertiser_id' => $advertiser_id,
                    'advertiser_user_id' => $advertiser_user_id,
                    'platform' => PlatformEnum::TIKTOL,
                    'entity_id' => $entity_id
                ]);
                if (empty($advertiserModel)) {
                    $advertiserModel = new AdsAccount();
                    $advertiserModel->entity_id = $entity_id;
                }

                if ($userInfo['data']) {
                    $advertiserModel->email = $userInfo['data']['email'];
                    $advertiserModel->advertiser_user_id = $userInfo['data']['id']; //用户id
                    $advertiserModel->advertiser_user_name = $userInfo['data']['display_name']; //用户名
                }

                $advertiserModel->access_token = $access_token['data']['access_token']; //账户access_token
                $advertiserModel->refresh_token = $access_token['data']['refresh_token'];
                $advertiserModel->ad_name = $access_token['data']['advertiser_name']; //账号名称
                $advertiserModel->advertiser_id = $advertiser_id;
                $advertiserModel->platform = PlatformEnum::TIKTOL;
                $advertiserModel->authorized_time = time();
                if (!$advertiserModel->save(false)) throw new \yii\db\Exception(current($advertiserModel->getErrors())[0]);
            }

            $trans->commit();
        } catch (\yii\db\Exception $e) {
            $trans->rollBack();
            return $e->getMessage();
        }

        return '巨量引擎授权成功';
    }

    /**
     * 巨量引擎-获取授权账号信息
     *
     * @param array $params
     * @return bool|mixed|string
     */
    public static function getOceanengineNewCallback($params = [])
    {
        echo '<pre>';
        print_r($params);
        exit;
    }

    /**
     * 朋友圈-获取授权账户信息
     *
     * @param array $params
     * @return string
     */
    public static function getTencentCallback($params = [])
    {
        $auth_code = $params["authorization_code"];
        $state = $params["state"];
        if (empty($auth_code)) return '微信朋友圈authorization_code为空';
        //        if (empty($state)) return '您授权的链接state不能为空';

        //        $entity_id = Entity::find()->where(['code' => $state])->scalar();
        $entity_id = 1;
        if (empty($entity_id)) return '您授权的企业不存在您的系统，请查看链接';

        $access_token = (new Tencent())->reqTenAccessToken($auth_code);
        if ($access_token['code'] != 0) return json_encode($access_token);

        $account_role_type = $access_token['data']['authorizer_info']['account_role_type'];

        if ($access_token['data']['authorizer_info']['account_type'] == 'ACCOUNT_TYPE_BM') {
            // if ($account_role_type != 'ACCOUNT_ROLE_TYPE_BUSINESS_MANAGER') {
            //     return 'ADQ授权授权失败，ADQ请使用身份为：商务管家账户授权';
            // }
            $platform = PlatformEnum::ADQ;
        } else {
            if ($account_role_type != 'ACCOUNT_ROLE_TYPE_AGENCY') {
                return '微信朋友圈授权授权失败，朋友圈请使用身份为：代理商账号或广告主账号授权';
            }
            $platform = PlatformEnum::WECHAT;
        }

        /** @var $advertiserModel AdsAccount * */
        $remark = '该账号已经授权过了';
        $advertiserModel = AdsAccount::findOne([
            'advertiser_id' => $access_token['data']['authorizer_info']['account_id'],
            'platform' => $platform,
            'advertiser_user_name' => $access_token['data']['authorizer_info']['login_name'],
            'entity_id' => $entity_id
        ]);

        if (empty($advertiserModel)) {
            $advertiserModel = new AdsAccount();
            $advertiserModel->entity_id = $entity_id;
            $remark = '';
        }

        $advertiserModel->access_token = $access_token['data']['access_token'];
        $advertiserModel->refresh_token = $access_token['data']['refresh_token'];
        $advertiserModel->ad_name = $access_token['data']['authorizer_info']['account_name'];
        $advertiserModel->advertiser_id = $access_token['data']['authorizer_info']['account_id'];
        $advertiserModel->platform = $platform;
        $advertiserModel->advertiser_user_id = $access_token['data']['authorizer_info']['wechat_account_id'];
        $advertiserModel->advertiser_user_name = $access_token['data']['authorizer_info']['login_name'];
        $advertiserModel->authorized_time = time();
        try {
            $res = $advertiserModel->save(false);
            if (!$res) throw new \yii\base\Exception(current($advertiserModel->getFirstErrors()));
        } catch (\yii\base\Exception $e) {
            return $e->getMessage();
        }

        return '微信朋友圈授权成功，' . $remark;
    }

    /**
     * 磁力引擎-获取授权账号信息
     *
     * @param array $params
     * @return bool|mixed|string
     */
    public static function getQuicklyCallback($params = [])
    {
        $auth_code = $params["auth_code"];
        $entity_id = $params["entity_id"];
        if (empty($auth_code)) return '磁力引擎授权auth_code为空';
        if (empty($entity_id)) return '您授权的链接entity_id不能为空,请联系信息部，到磁力引擎应用修改回调地址';

        $entityInfo = Entity::find()->where(['id' => $entity_id])->scalar();
        if (empty($entityInfo)) return '您授权的企业不存在您的系统，请查看链接';

        $tokenResult = (new Quickly())->getAccessToken($auth_code);

        if ($tokenResult['code'] != 0) return $tokenResult;
        $access_token = $tokenResult['data']['access_token'];
        $refresh_token = $tokenResult['data']['refresh_token'];

        $list = (new Quickly())->getApprovalList($access_token);
        if ($list['code'] != 1) {
            return '磁力引擎获取账户失败:' . $list['message'];
        }
        //开始处理账户数据
        try {
            foreach ($list['data']['details'] as $advertiser_id) {
                //开始获取账户信息
                $url = Quickly::baseUrl . 'rest/openapi/v1/advertiser/info/';
                $userInfo = Tool::promoteGet($url, ['advertiser_id' => $advertiser_id], $access_token);
                $advertiser_user_id = empty($userInfo['data']['user_id']) ? 0 : $userInfo['data']['user_id'];

                /** @var $advertiserModel AdsAccount * */
                $advertiserModel = AdsAccount::findOne([
                    'advertiser_id' => $advertiser_id,
                    'advertiser_user_id' => $advertiser_user_id,
                    'platform' => PlatformEnum::QUICKLY,
                    'entity_id' => $entity_id
                ]);
                if (empty($advertiserModel)) {
                    $advertiserModel = new AdsAccount();
                    $advertiserModel->entity_id = $entity_id;
                }

                if ($userInfo['data']) {
                    $advertiserModel->advertiser_user_id = $userInfo['data']['user_id']; //用户id
                    $advertiserModel->advertiser_user_name = $userInfo['data']['user_name']; //账户名称
                }

                $advertiserModel->access_token = $access_token; //账户access_token
                $advertiserModel->refresh_token = $refresh_token;
                $advertiserModel->ad_name = $userInfo['data']['user_name']; //账号名称
                $advertiserModel->advertiser_id = $advertiser_id;
                $advertiserModel->platform = PlatformEnum::QUICKLY;
                $advertiserModel->authorized_time = time();
                if (!$advertiserModel->save(false)) throw new \yii\db\Exception(current($advertiserModel->getErrors())[0]);

                //创建、更新子账户
                $accountSub = AdsAccountSub::findOne(['td_id' => $advertiserModel->id, 'sub_advertiser_id' => $advertiserModel->advertiser_id, 'entity_id' => $entity_id]);
                if (empty($accountSub)) {
                    $promote = PromoteChannel::find()->select('id')->where(['entity_id' => $entity_id, 'platform' => PlatformEnum::QUICKLY])->one();
                    $accountSub = new AdsAccountSub();
                    $accountSub->td_id = $advertiserModel->id;
                    $accountSub->sub_advertiser_id = $advertiserModel->advertiser_id;
                    $accountSub->way = AdsAccountWayEnum::AUTO;
                    $accountSub->status = AdsAccountSubStatusEnum::STANDBY;
                    $accountSub->entity_id = $advertiserModel->entity_id;
                    $accountSub->promote_id = $promote->id ?? 0;
                    $accountSub->rebates = 1;
                }
                $accountSub->sub_advertiser_name = $advertiserModel->ad_name;
                if ($accountSub->main_body_id) {
                    $accountSub->main_body_id = AdsAccountSub::getMainBodyIdByName($advertiserModel->ad_name, $accountSub->entity_id);
                }
                if (!$accountSub->save(false)) throw new \yii\db\Exception(current($accountSub->getErrors())[0]);
            }
        } catch (\yii\base\Exception $e) {
            return $e->getMessage();
        }

        return '快手磁力引擎授权成功';
    }
}
