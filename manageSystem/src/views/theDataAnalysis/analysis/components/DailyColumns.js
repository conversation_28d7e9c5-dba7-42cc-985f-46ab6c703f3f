/*
 * @Author: chending “<EMAIL>”
 * @Date: 2022-06-25 14:25:16
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-04-11 15:18:53
 * @FilePath: \manageSystem\src\views\theDataAnalysis\analysis\columns.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const analyServiceColumns = [
    {
        dataIndex: "date",
        title: "日期",
        key: "date",
        align: "left",
        fixed: 'left',
        width: 200,
        scopedSlots: { customRender: 'date' },
    },
    {
        dataIndex: "store_name",
        title: "门店名称",
        key: "store_name",
        align: "left",
        fixed: 'left',
        width: 200,
        scopedSlots: { customRender: 'store_name' },
    },
    {
        dataIndex: "area_text",
        title: "所属区域",
        key: "area_text",
        align: "left",
        fixed: 'left',
        width: 150
    },
    {
        dataIndex: "received_amount",
        key: "received_amount",
        align: "right",
        width: 140,
        slots: {
            title: "received_amount",
            slotName: "实收业绩",
            slotText: "门店实收+储值卡实耗+团购收款" ///
        }
    },
    {
        dataIndex: "refund_amount",
        key: "refund_amount",
        align: "right",
        width: 140,
        slots: {
            title: "refund_amount",
            slotName: "退款金额",
            slotText: "门店退款金额"
        }
    },
    {
        dataIndex: "refund_rate",
        key: "refund_rate",
        align: "right",
        width: 140,
        slots: {
            title: "refund_rate",
            slotName: "退款率",
            slotText: "退款率=退款金额/实收业绩"
        }
    },
    // {
    //     dataIndex: "divide_amount",
    //     key: "divide_amount",
    //     align: "right",
    //     sorter: false,
    //     width: 180,
    //     slots: {
    //         title: "divide_amount",
    //         slotName: "门店分成业绩",
    //         slotText: "新客到店首日产生的门店实收" ///
    //     }
    // },
    {
        dataIndex: "new_customer",
        key: "new_customer",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "new_customer",
            slotName: "新客到店人数",
            slotText: `渠道不等于”老带新”或到店首日仅消费不参与老师业结商品的新客人数`
        }
    },
    {
        dataIndex: "new_average",
        key: "new_average",
        align: "right",
        width: 160,
        slots: {
            title: "new_average",
            slotName: "新客均产值",
            slotText: "新客均产值=实收业绩/新客到店人数"
        }
    },
    // {//作废，暂时隐藏
    //     dataIndex: "new_transaction_num",
    //     key: "new_transaction_num",
    //     align: "right",
    //     width: 160,
    //     slots: {
    //         title: "new_transaction_num",
    //         slotName: "新客成交人数",
    //         slotText: "搜索时间段前未下过单的客户，在搜索时间段内计为1个新客，按手机号去重"
    //     }
    // },
    {
        dataIndex: "new_transaction_amount",
        key: "new_transaction_amount",
        align: "right",
        sorter: false,
        width: 130,
        slots: {
            title: "new_transaction_amount",
            slotName: "新客首日成交业绩",
            slotText: "新客成交人数到店首日产生的门店实收+储值卡实耗+团购收款"
        }
    },
    {
        dataIndex: "new_unit_price",
        key: "new_unit_price",
        align: "right",
        width: 160,
        slots: {
            title: "new_unit_price",
            slotName: "新客客单价",
            slotText: "新客首日成交业绩除以新客成交人数"
        }
    },
    {
        dataIndex: "old_customer",
        key: "old_customer",
        align: "right",
        width: 160,
        slots: {
            title: "old_customer",
            slotName: "老客到店人数",
            slotText: "搜索时间段内老客的到店人数，按手机号去重"
        }
    },
    // {
    //     dataIndex: "old_customer_frequency",
    //     key: "old_customer_frequency",
    //     align: "right",
    //     width: 130,
    //     slots: {
    //         title: "old_customer_frequency",
    //         slotName: "老客到店人次",
    //         slotText: "搜索时间段内老客的到店次数，一人一天记为一次"
    //     }
    // },
    {
        dataIndex: "old_amount",
        key: "old_amount",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "old_amount",
            slotName: "老客到店业绩",
            slotText: "搜索时间段内老客的到店产生的门店实收+储值卡实耗+团购收款"
        }
    },
    {
        dataIndex: "old_unit_price", ///
        key: "old_unit_price",
        align: "right",
        width: 160,
        slots: {
            title: "old_unit_price",
            slotName: "老客客单价",
            slotText: "老客到店业绩除以老客到店人数"
        }
    },
    // {
    //     dataIndex: "old_customer_frequency_price",  ////////////
    //     key: "old_customer_frequency_price",
    //     align: "right",
    //     width: 160,
    //     slots: {
    //         title: "old_customer_frequency_price",
    //         slotName: "老客次单价",
    //         slotText: "老客到店业绩除以老客到店人次"
    //     }
    // },
    {
        dataIndex: "loss_num",
        key: "loss_num",
        align: "right",
        width: 120,
        slots: {
            title: "loss_num",
            slotName: "流失人数",
            // slotText: "客服统计的流失人数，在搜索时间段内流失客户如果存在已完成的单子，则不会被记为流失"
            slotText: "流失人数"
        }
    },
    {
        dataIndex: "total_price",
        key: "total_price",
        align: "right",
        width: 120,
        slots: {
            title: "total_price",
            slotName: "客单价(包含流失)",
            // slotText: "客服统计的流失人数，在搜索时间段内流失客户如果存在已完成的单子，则不会被记为流失"
            slotText: "客单价=实收业绩/(新客到店人数+流失人数)"
        }
    },
    {
        dataIndex: "teacher_num",
        key: "teacher_num",
        align: "right",
        width: 120,
        slots: {
            title: "teacher_num",
            slotName: "老师人数",
            slotText: "老师人数"
        }
    },
    {
        dataIndex: "customer_service_num",
        key: "customer_service_num",
        align: "right",
        width: 120,
        slots: {
            title: "customer_service_num",
            slotName: "可接待客户数",
            slotText: "可接待客户数"
        }
    },
    {
        dataIndex: "full_load_rate",
        key: "full_load_rate",
        align: "right",
        width: 120,
        slots: {
            title: "full_load_rate",
            slotName: "满载率",
            slotText: "满载率=新客到店人数/可接待客户数"
        }
    },
    {
        dataIndex: "reschedule_num",
        key: "reschedule_num",
        align: "right",
        width: 120,
        slots: {
            title: "reschedule_num",
            slotName: "改期数",
            slotText: "改期数"
        }
    },
    // {
    //     dataIndex: "loss_rate",
    //     key: "loss_rate",
    //     align: "right",
    //     sorter: false,
    //     width: 160,
    //     slots: {
    //         title: "loss_rate",
    //         slotName: "流失率",
    //         slotText: "流失人数除以(新客人数+流失人数)"
    //     }
    // },
]
export const header = (key = '') => analyServiceColumns.reduce((l, j) => {
    if (j.expand && Array.isArray(j.expand) && j.expand.length > 0) {
        j.expand.forEach(item => {
            l.push(item)
        })
    } else {
        l.push(j)
    }
    return l
}, []).filter(item => item.title != key).map(item => item.title || item.slots.slotName)
export const exportKey = (...keys) => analyServiceColumns.reduce((l, j) => {
    if (j.expand && Array.isArray(j.expand) && j.expand.length > 0) {
        j.expand.forEach(item => {
            l.push(item)
        })
    } else {
        l.push(j)
    }
    return l
}, []).filter(item => {
    return !keys.includes(item.dataIndex)
}).map(item => item.dataIndex)